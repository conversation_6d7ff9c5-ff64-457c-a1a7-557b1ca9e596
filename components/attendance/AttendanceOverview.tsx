import React from "react";
import { StyleSheet, View, Text } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  FadeIn,
} from "react-native-reanimated";
import { Colors } from "@/constants/Colors";
import { Card } from "@/components/ui/Card";
import { transformAttendanceData, GraphQLAttendance } from "./utils";

// Define attendance status types
export enum AttendanceStatus {
  ONTIME = "ontime",
  LATE = "late",
  ABSENT = "absent",
  FUTURE = "future",
}

// Define attendance data structure
export interface DailyAttendance {
  day: string;
  records: AttendanceStatus[];
}

// Define attendance summary data structure
export interface AttendanceSummary {
  ontime: number;
  late: number;
  absent: number;
}

interface AttendanceOverviewProps {
  attendanceData: GraphQLAttendance[];
  selectedMonth: Date;
}

/**
 * Attendance Overview Component
 * Displays a grid of attendance records and summary statistics
 */
export function AttendanceOverview({
  attendanceData,
  selectedMonth,
}: AttendanceOverviewProps) {
  // Transform the GraphQL data into the format expected by the component
  const data = React.useMemo(() => {
    return transformAttendanceData(attendanceData, selectedMonth);
  }, [attendanceData, selectedMonth]);

  // Animation values
  const cardScale = useSharedValue(0.95);
  const cardOpacity = useSharedValue(0);

  // Generate a stable key for triggering grid re-animation when data changes
  const animationKey = React.useMemo(() => {
    // Create a key based on data length and selected month to trigger re-animation
    const dataLength = attendanceData?.length || 0;
    const monthKey = selectedMonth.getTime();
    return `${dataLength}-${monthKey}`;
  }, [attendanceData?.length, selectedMonth]);

  // Start animations when component mounts or data changes
  React.useEffect(() => {
    // Reset and animate card
    cardScale.value = 0.95;
    cardOpacity.value = 0;

    cardScale.value = withTiming(1, {
      duration: 500,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
    cardOpacity.value = withTiming(1, { duration: 600 });
  }, [attendanceData, selectedMonth]);

  // Card animated style
  const cardAnimatedStyle = useAnimatedStyle(() => ({
    opacity: cardOpacity.value,
    transform: [{ scale: cardScale.value }],
  }));

  // Get color for attendance status
  const getStatusColor = (status: AttendanceStatus) => {
    switch (status) {
      case AttendanceStatus.ONTIME:
        return Colors.primary;
      case AttendanceStatus.LATE:
        // Lighter orange color
        return "#FFB74D";
      case AttendanceStatus.ABSENT:
        return Colors.error;
      case AttendanceStatus.FUTURE:
        return Colors.lightGray;
      default:
        return Colors.lightGray;
    }
  };

  // Calculate percentage for summary
  const calculatePercentage = (value: number) => {
    const total = data.summary.ontime + data.summary.late + data.summary.absent;
    return total > 0 ? Math.round((value / total) * 100) : 0;
  };

  // Show empty state if no data
  if (!data.dailyAttendance.length) {
    return (
      <Animated.View style={cardAnimatedStyle}>
        <Card style={styles.card}>
          <Text style={styles.title}>Attendance</Text>
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              No attendance data available for this month
            </Text>
          </View>
        </Card>
      </Animated.View>
    );
  }

  return (
    <View>
      {/* Attendance Card */}
      <Animated.View style={cardAnimatedStyle}>
        <Card style={styles.card}>
          <Text style={styles.title}>Attendance</Text>

          {/* Attendance Grid */}
          <View style={styles.gridContainer}>
            {data.dailyAttendance.map((day, dayIndex) => (
              <View key={`${animationKey}-${dayIndex}`} style={styles.dayRow}>
                <View style={styles.dayRecords}>
                  {day.records.map((status, recordIndex) => (
                    <Animated.View
                      key={`${animationKey}-${dayIndex}-${recordIndex}`}
                      entering={FadeIn.delay(
                        50 * (dayIndex + recordIndex)
                      ).duration(300)}
                      style={[
                        styles.statusBox,
                        { backgroundColor: getStatusColor(status) },
                      ]}
                    />
                  ))}
                </View>
              </View>
            ))}
          </View>

          {/* Summary Section */}
          <Animated.View
            key={`summary-${animationKey}`}
            entering={FadeIn.delay(300).duration(400)}
            style={styles.summaryContainer}
          >
            <Animated.View
              entering={FadeIn.delay(350).duration(300)}
              style={styles.summaryItem}
            >
              <View style={styles.summaryLabelContainer}>
                <View
                  style={[
                    styles.summaryIndicator,
                    { backgroundColor: Colors.primary },
                  ]}
                />
                <Text style={styles.summaryLabel}>On-time</Text>
              </View>
              <Text style={styles.summaryValue}>
                {calculatePercentage(data.summary.ontime)}%
              </Text>
            </Animated.View>

            <Animated.View
              entering={FadeIn.delay(400).duration(300)}
              style={styles.summaryItem}
            >
              <View style={styles.summaryLabelContainer}>
                <View
                  style={[
                    styles.summaryIndicator,
                    { backgroundColor: "#FFB74D" },
                  ]}
                />
                <Text style={styles.summaryLabel}>Late</Text>
              </View>
              <Text style={styles.summaryValue}>
                {calculatePercentage(data.summary.late)}%
              </Text>
            </Animated.View>

            <Animated.View
              entering={FadeIn.delay(450).duration(300)}
              style={styles.summaryItem}
            >
              <View style={styles.summaryLabelContainer}>
                <View
                  style={[
                    styles.summaryIndicator,
                    { backgroundColor: Colors.error },
                  ]}
                />
                <Text style={styles.summaryLabel}>Absent</Text>
              </View>
              <Text style={styles.summaryValue}>
                {calculatePercentage(data.summary.absent)}%
              </Text>
            </Animated.View>
          </Animated.View>
        </Card>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 16,
    marginVertical: 12,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 16,
  },
  gridContainer: {
    marginBottom: 20,
  },
  dayRow: {
    marginBottom: 12,
  },
  dayRecords: {
    flex: 1,
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 4,
  },
  statusBox: {
    width: 24,
    height: 24,
    borderRadius: 4,
  },
  summaryContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  summaryItem: {
    flex: 1,
    alignItems: "center",
  },
  summaryLabelContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  summaryIndicator: {
    width: 16,
    height: 4,
    borderRadius: 2,
    marginRight: 6,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.text,
  },
  emptyContainer: {
    paddingVertical: 40,
    alignItems: "center",
    justifyContent: "center",
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: "center",
  },
});
